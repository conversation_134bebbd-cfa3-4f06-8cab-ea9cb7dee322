import { theme } from '@/constants/theme';
import { Ionicons } from '@expo/vector-icons';
import React, { useEffect, useState } from 'react';
import {
  Alert,
  SafeAreaView,
  ScrollView,
  StatusBar,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import { DataService } from '../services/dataService';
import { FamilyMember, MedicationWithMembers } from '../types/medication';

export default function DashboardScreen() {
  const [medications, setMedications] = useState<MedicationWithMembers[]>([]);
  const [familyMembers, setFamilyMembers] = useState<FamilyMember[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      await DataService.initializeData();
      const [meds, members] = await Promise.all([
        DataService.getMedications(),
        DataService.getFamilyMembers(),
      ]);

      // Combine medications with their assigned family members
      const medicationsWithMembers: MedicationWithMembers[] = meds.map(med => ({
        ...med,
        members: members.filter(member => med.assignedMembers.includes(member.id)),
      }));

      setMedications(medicationsWithMembers);
      setFamilyMembers(members);
    } catch (error) {
      console.error('Error loading data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleTakeDose = async (medicationId: string) => {
    try {
      await DataService.takeDose(medicationId);
      Alert.alert('Success', 'Dose recorded successfully!');
    } catch (error) {
      Alert.alert('Error', 'Failed to record dose');
    }
  };

  const getStockColor = (stockLevel: string) => {
    switch (stockLevel) {
      case 'good': return theme.colors.stockGood;
      case 'low': return theme.colors.stockLow;
      case 'critical': return theme.colors.stockCritical;
      default: return theme.colors.textTertiary;
    }
  };

  const getStockWidth = (daysLeft: number) => {
    if (daysLeft > 20) return '100%';
    if (daysLeft > 10) return '60%';
    if (daysLeft > 5) return '30%';
    return '15%';
  };

  const renderMedicationCard = (medication: MedicationWithMembers) => (
    <View key={medication.id} style={styles.medicationCard}>
      <View style={styles.medicationHeader}>
        <View style={styles.medicationInfo}>
          <Text style={styles.medicationName}>
            {medication.name} {medication.dosage && `(${medication.dosage})`}
          </Text>
          {medication.stockLevel === 'critical' && (
            <View style={styles.lowStockBadge}>
              <Text style={styles.lowStockText}>LOW STOCK!</Text>
            </View>
          )}
        </View>
        <Ionicons name="checkmark-circle" size={24} color={theme.colors.success} />
      </View>

      <View style={styles.membersContainer}>
        {medication.members.map(member => (
          <View
            key={member.id}
            style={[
              styles.memberTag,
              { backgroundColor: member.color },
              member.type === 'child' && styles.childMemberTag,
            ]}
          >
            <Ionicons
              name={member.type === 'adult' ? 'person' : 'lock-closed'}
              size={12}
              color="white"
            />
            <Text style={styles.memberName}>{member.name}</Text>
          </View>
        ))}
      </View>

      <View style={styles.stockContainer}>
        <View style={styles.stockBar}>
          <View
            style={[
              styles.stockFill,
              {
                backgroundColor: getStockColor(medication.stockLevel),
                width: getStockWidth(medication.daysLeft),
              },
            ]}
          />
        </View>
        <Text style={styles.daysLeft}>Est. {medication.daysLeft} days left</Text>

        {medication.members.some(m => m.type === 'adult') && (
          <TouchableOpacity
            style={styles.takeDoseButton}
            onPress={() => handleTakeDose(medication.id)}
          >
            <Text style={styles.takeDoseText}>Take Dose</Text>
          </TouchableOpacity>
        )}
      </View>
    </View>
  );

  const adultMedications = medications.filter(med =>
    med.members.some(member => member.type === 'adult')
  );

  const childMedications = medications.filter(med =>
    med.members.some(member => member.type === 'child')
  );

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <Text>Loading...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" backgroundColor={theme.colors.background} />

      <View style={styles.header}>
        <Text style={styles.headerTitle}>Family Medication Inventory</Text>
        <TouchableOpacity style={styles.askButton}>
          <Ionicons name="chatbubble-outline" size={20} color={theme.colors.primary} />
          <Text style={styles.askButtonText}>Ask a Question</Text>
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {adultMedications.length > 0 && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Adults' Medications</Text>
            {adultMedications.map(renderMedicationCard)}
          </View>
        )}

        {childMedications.length > 0 && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Kids' Medications</Text>
            {childMedications.map(renderMedicationCard)}
          </View>
        )}
      </ScrollView>

      <TouchableOpacity style={styles.fab}>
        <Ionicons name="add" size={24} color="white" />
        <Text style={styles.fabText}>Add Family Member</Text>
      </TouchableOpacity>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: theme.spacing.lg,
    paddingVertical: theme.spacing.md,
    backgroundColor: theme.colors.surface,
    ...theme.shadows.sm,
  },
  headerTitle: {
    fontSize: theme.typography.xl,
    fontWeight: theme.typography.bold,
    color: theme.colors.textPrimary,
  },
  askButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: `${theme.colors.primary}15`,
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.xs,
    borderRadius: theme.borderRadius.xl,
  },
  askButtonText: {
    color: theme.colors.primary,
    marginLeft: theme.spacing.xs,
    fontSize: theme.typography.sm,
    fontWeight: theme.typography.medium,
  },
  content: {
    flex: 1,
    paddingHorizontal: theme.spacing.lg,
  },
  section: {
    marginVertical: theme.spacing.lg,
  },
  sectionTitle: {
    fontSize: theme.typography.lg,
    fontWeight: theme.typography.semibold,
    color: theme.colors.textPrimary,
    marginBottom: theme.spacing.md,
  },
  medicationCard: {
    backgroundColor: theme.colors.surface,
    borderRadius: theme.borderRadius.md,
    padding: theme.spacing.md,
    marginBottom: theme.spacing.md,
    ...theme.shadows.md,
  },
  medicationHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: theme.spacing.md,
  },
  medicationInfo: {
    flex: 1,
  },
  medicationName: {
    fontSize: theme.typography.base,
    fontWeight: theme.typography.semibold,
    color: theme.colors.textPrimary,
    marginBottom: theme.spacing.xs,
  },
  lowStockBadge: {
    backgroundColor: theme.colors.error,
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: 2,
    borderRadius: theme.borderRadius.sm,
    alignSelf: 'flex-start',
  },
  lowStockText: {
    color: 'white',
    fontSize: theme.typography.xs,
    fontWeight: theme.typography.bold,
  },
  membersContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: theme.spacing.md,
  },
  memberTag: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: theme.spacing.xs,
    borderRadius: theme.borderRadius.md,
    marginRight: theme.spacing.sm,
    marginBottom: theme.spacing.xs,
  },
  childMemberTag: {
    backgroundColor: theme.colors.warning,
  },
  memberName: {
    color: 'white',
    fontSize: theme.typography.xs,
    fontWeight: theme.typography.medium,
    marginLeft: theme.spacing.xs,
  },
  stockContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  stockBar: {
    flex: 1,
    height: 6,
    backgroundColor: theme.colors.border,
    borderRadius: 3,
    marginRight: theme.spacing.md,
  },
  stockFill: {
    height: '100%',
    borderRadius: 3,
  },
  daysLeft: {
    fontSize: theme.typography.xs,
    color: theme.colors.textSecondary,
    marginRight: theme.spacing.md,
  },
  takeDoseButton: {
    backgroundColor: theme.colors.primary,
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.xs,
    borderRadius: theme.borderRadius.lg,
  },
  takeDoseText: {
    color: 'white',
    fontSize: theme.typography.xs,
    fontWeight: theme.typography.medium,
  },
  fab: {
    position: 'absolute',
    bottom: theme.spacing.xl,
    right: theme.spacing.xl,
    backgroundColor: theme.colors.primary,
    borderRadius: theme.borderRadius.xl + 8,
    paddingHorizontal: theme.spacing.lg,
    paddingVertical: theme.spacing.md,
    flexDirection: 'row',
    alignItems: 'center',
    ...theme.shadows.lg,
  },
  fabText: {
    color: 'white',
    marginLeft: theme.spacing.sm,
    fontWeight: theme.typography.medium,
  },
});
